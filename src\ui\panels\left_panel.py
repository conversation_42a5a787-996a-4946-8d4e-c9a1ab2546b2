"""
左侧面板实现
"""

from PySide6.QtWidgets import (
    QFrame, QVBoxLayout, QLabel, QWidget, QScrollArea
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap

from ...utils.constants import Colors, Styles
from ..accordions.song_manager_accordion import SongManagerAccordion
from ..accordions.api_manager_accordion import ApiManagerAccordion
from ..components.upload_widget import UploadWidget


class LeftPanel(QFrame):
    """左侧面板类"""
    
    def __init__(self):
        super().__init__()
        self.setProperty("class", "panel")
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        # 创建主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(Styles.PADDING_NORMAL, Styles.PADDING_NORMAL, 
                                 Styles.PADDING_NORMAL, Styles.PADDING_NORMAL)
        layout.setSpacing(Styles.MARGIN_NORMAL)
        
        # Logo区域
        self.create_logo_area(layout)
        
        # 创建滚动区域包含所有内容，确保手风琴展开时不被遮盖
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # 主内容容器 - 包含手风琴和上传组件
        main_container = QWidget()
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(Styles.MARGIN_NORMAL)

        # 手风琴容器
        accordion_container = QWidget()
        accordion_layout = QVBoxLayout(accordion_container)
        accordion_layout.setContentsMargins(0, 0, 0, 0)
        accordion_layout.setSpacing(Styles.MARGIN_SMALL)

        # 歌曲管理手风琴
        self.song_manager = SongManagerAccordion()
        accordion_layout.addWidget(self.song_manager)

        # API管理手风琴
        self.api_manager = ApiManagerAccordion()
        accordion_layout.addWidget(self.api_manager)

        # 将手风琴容器添加到主容器
        main_layout.addWidget(accordion_container)

        # 上传处理区域 - 放在滚动区域内
        self.upload_widget = UploadWidget()
        main_layout.addWidget(self.upload_widget)

        # 添加弹性空间确保内容可以正确滚动
        main_layout.addStretch()

        scroll_area.setWidget(main_container)
        layout.addWidget(scroll_area, 1)  # 占用剩余空间

        # 设置滚动区域样式
        scroll_area.setStyleSheet(f"""
        QScrollArea {{
            background-color: transparent;
            border: none;
        }}
        QScrollBar:vertical {{
            background-color: {Colors.SECONDARY_BG};
            width: 8px;
            border-radius: 4px;
        }}
        QScrollBar::handle:vertical {{
            background-color: {Colors.BORDER};
            border-radius: 4px;
            min-height: 20px;
        }}
        QScrollBar::handle:vertical:hover {{
            background-color: {Colors.ACCENT};
        }}
        """)
        
    def create_logo_area(self, layout):
        """创建Logo区域 - 参考HTML版本居中布局"""
        # 创建居中容器
        logo_container = QFrame()
        logo_container_layout = QVBoxLayout(logo_container)
        logo_container_layout.setContentsMargins(0, 0, 0, 0)
        logo_container_layout.setSpacing(0)

        # Logo图片容器 - 确保居中
        logo_frame = QFrame()
        logo_frame.setFixedHeight(80)  # 固定高度以匹配HTML版本
        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setContentsMargins(0, 0, 0, 0)
        logo_layout.setSpacing(0)

        # Logo图片
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)

        # 尝试加载Logo图片
        try:
            pixmap = QPixmap("assets/images/logo.png")
            if not pixmap.isNull():
                # 缩放Logo到合适大小 (参考HTML版本的h-16)
                scaled_pixmap = pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                logo_label.setPixmap(scaled_pixmap)
            else:
                raise FileNotFoundError
        except:
            # 如果没有Logo图片，显示文字
            logo_label.setText("🎵 木偶AI")
            logo_label.setProperty("class", "header")
            logo_label.setStyleSheet(f"""
            QLabel {{
                color: {Colors.ACCENT};
                font-size: 24px;
                font-weight: bold;
                padding: {Styles.PADDING_LARGE}px;
            }}
            """)

        logo_layout.addWidget(logo_label, 0, Qt.AlignCenter)
        logo_container_layout.addWidget(logo_frame, 0, Qt.AlignCenter)

        layout.addWidget(logo_container)
