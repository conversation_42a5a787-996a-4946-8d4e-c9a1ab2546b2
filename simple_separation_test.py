#!/usr/bin/env python3
"""
简化的音频分离API测试脚本
"""

import requests
import time
import os

# API服务器配置
API_BASE_URL = "http://127.0.0.1:8001"

def test_separation_api():
    """测试音频分离API"""
    
    # 测试文件路径
    test_audio_path = "0-other/测试人声.wav"
    
    if not os.path.exists(test_audio_path):
        print(f"❌ 测试音频文件不存在: {test_audio_path}")
        return False
    
    print("🚀 开始测试音频分离API...")
    
    try:
        # 1. 检查API健康状态
        print("1. 检查API健康状态...")
        health_response = requests.get(f"{API_BASE_URL}/health", timeout=10)
        if health_response.status_code != 200:
            print(f"❌ API服务不可用: {health_response.status_code}")
            return False
        
        health_data = health_response.json()
        print(f"✅ API服务正常: {health_data['status']}")
        
        # 2. 上传测试音频文件
        print("\n2. 上传测试音频文件...")
        with open(test_audio_path, 'rb') as f:
            files = {'file': (os.path.basename(test_audio_path), f, 'audio/wav')}
            upload_response = requests.post(f"{API_BASE_URL}/upload", files=files, timeout=30)
        
        if upload_response.status_code != 200:
            print(f"❌ 文件上传失败: {upload_response.status_code}")
            print(upload_response.text)
            return False
        
        upload_data = upload_response.json()
        file_id = upload_data['file_id']
        print(f"✅ 文件上传成功: {file_id}")
        
        # 3. 开始音频分离
        print("\n3. 开始音频分离...")
        separation_request = {
            "mode": "complete",
            "output_format": "wav",
            "model_name": "bandit_v2"
        }
        
        separate_response = requests.post(
            f"{API_BASE_URL}/separate",
            params={"file_id": file_id},
            json=separation_request,
            timeout=30
        )
        
        if separate_response.status_code != 200:
            print(f"❌ 音频分离请求失败: {separate_response.status_code}")
            print(separate_response.text)
            return False
        
        separate_data = separate_response.json()
        task_id = separate_data['task_id']
        print(f"✅ 音频分离任务已创建: {task_id}")
        
        # 4. 监控分离进度
        print("\n4. 监控分离进度...")
        max_wait_time = 600  # 最大等待10分钟
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            task_response = requests.get(f"{API_BASE_URL}/tasks/{task_id}", timeout=10)
            if task_response.status_code != 200:
                print(f"❌ 获取任务状态失败: {task_response.status_code}")
                return False
            
            task_data = task_response.json()
            status = task_data['status']
            progress = task_data['progress']
            message = task_data['message']
            
            print(f"   状态: {status}, 进度: {progress:.1%}, 消息: {message}")
            
            if status == "completed":
                print("✅ 音频分离完成!")
                result = task_data.get('result', {})
                print(f"   分离结果: {result}")
                
                # 检查输出文件
                if result:
                    for stem_name, file_path in result.items():
                        if os.path.exists(file_path):
                            file_size = os.path.getsize(file_path)
                            print(f"   {stem_name}: {file_path} ({file_size} 字节)")
                        else:
                            print(f"   {stem_name}: {file_path} (文件不存在)")
                    print("✅ 音频分离API测试成功!")
                    return True
                else:
                    print("❌ 未找到分离结果文件")
                    return False
            elif status == "error":
                error = task_data.get('error', '未知错误')
                print(f"❌ 音频分离失败: {error}")
                return False
            elif status == "cancelled":
                print("❌ 任务被取消")
                return False
            
            time.sleep(5)  # 等待5秒后再次检查
        
        print("❌ 分离超时")
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = test_separation_api()
    if success:
        print("\n🎉 音频分离API测试成功!")
    else:
        print("\n💥 音频分离API测试失败!")
    
    exit(0 if success else 1)
