#!/usr/bin/env python3
"""
直接测试MSST分离功能（不通过API）
"""

import os
import subprocess
import time
import shutil
from pathlib import Path

def test_direct_msst_separation():
    """直接测试MSST分离"""
    
    print("🚀 开始直接MSST分离测试...")
    
    # 测试参数
    input_file = "0-other/测试人声.wav"
    temp_input_dir = "temp/test_input"
    output_dir = "outputs/msst_test_output"
    preset_path = "msst/preset.json"
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    # 检查预设文件
    if not os.path.exists(preset_path):
        print(f"❌ 预设文件不存在: {preset_path}")
        return False
    
    # 创建临时输入目录
    Path(temp_input_dir).mkdir(parents=True, exist_ok=True)
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 复制输入文件到临时目录
    temp_input_file = Path(temp_input_dir) / Path(input_file).name
    shutil.copy2(input_file, temp_input_file)
    print(f"✅ 输入文件已复制到: {temp_input_file}")
    
    # 构建命令
    python_path = "workenv/python.exe"
    msst_script_path = "msst/scripts/preset_infer_cli.py"
    
    cmd_args = [
        python_path,
        msst_script_path,
        "-p", preset_path,
        "-i", temp_input_dir,
        "-o", output_dir,
        "-f", "wav"
    ]
    
    print(f"执行命令: {' '.join(cmd_args)}")
    
    try:
        # 执行分离
        start_time = time.time()
        result = subprocess.run(
            cmd_args,
            capture_output=True,
            text=True,
            timeout=600,  # 10分钟超时
            encoding='utf-8',
            errors='ignore'  # 忽略编码错误
        )
        end_time = time.time()
        
        print(f"分离耗时: {end_time - start_time:.2f} 秒")
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 分离成功!")
            
            # 检查输出文件
            output_path = Path(output_dir)
            output_files = []
            
            for file_path in output_path.rglob("*"):
                if file_path.is_file() and file_path.suffix.lower() in ['.wav', '.mp3', '.flac']:
                    file_size = file_path.stat().st_size
                    output_files.append((str(file_path), file_size))
                    print(f"输出文件: {file_path} ({file_size} 字节)")
            
            if output_files:
                print("✅ MSST分离测试成功!")
                return True
            else:
                print("❌ 未找到输出文件")
                return False
        else:
            print(f"❌ 分离失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 分离超时")
        return False
    except Exception as e:
        print(f"❌ 分离过程中发生错误: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            if os.path.exists(temp_input_dir):
                shutil.rmtree(temp_input_dir)
                print("✅ 临时文件已清理")
        except:
            pass

if __name__ == "__main__":
    success = test_direct_msst_separation()
    if success:
        print("\n🎉 直接MSST分离测试成功!")
    else:
        print("\n💥 直接MSST分离测试失败!")
    
    exit(0 if success else 1)
