"""
样式表管理器
"""

from .constants import Colors, Styles

class StyleManager:
    """样式管理器，提供统一的样式表"""
    
    @staticmethod
    def get_main_window_style():
        """主窗口样式"""
        return f"""
        QMainWindow {{
            background-color: {Colors.BACKGROUND};
            color: {Colors.FOREGROUND};
            font-family: {Styles.FONT_FAMILY};
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        """
    
    @staticmethod
    def get_splitter_style():
        """分割器样式"""
        return f"""
        QSplitter::handle {{
            background-color: {Colors.BORDER};
            width: 2px;
            height: 2px;
        }}
        QSplitter::handle:hover {{
            background-color: {Colors.ACCENT};
        }}
        """
    
    @staticmethod
    def get_panel_style():
        """面板样式"""
        return f"""
        QFrame[class="panel"] {{
            background-color: {Colors.CARD_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.CARD_RADIUS};
            padding: {Styles.PADDING_NORMAL}px;
        }}
        """
    
    @staticmethod
    def get_accordion_style():
        """手风琴样式"""
        return f"""
        QFrame[class="accordion"] {{
            background-color: {Colors.CARD_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.CARD_RADIUS};
            margin: {Styles.MARGIN_SMALL}px 0px;
        }}
        
        QPushButton[class="accordion-header"] {{
            background-color: {Colors.SECONDARY_BG};
            border: none;
            border-radius: {Styles.INPUT_RADIUS};
            padding: {Styles.PADDING_NORMAL}px;
            text-align: left;
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_LARGE}px;
            color: {Colors.FOREGROUND};
        }}
        
        QPushButton[class="accordion-header"]:hover {{
            background-color: {Colors.ACCENT};
        }}
        
        QPushButton[class="accordion-header"]:pressed {{
            background-color: {Colors.ACCENT_PRESSED};
        }}
        """
    
    @staticmethod
    def get_button_style():
        """按钮样式"""
        return f"""
        QPushButton {{
            background-color: {Colors.ACCENT};
            border: none;
            border-radius: {Styles.BUTTON_RADIUS};
            padding: {Styles.PADDING_NORMAL}px {Styles.PADDING_LARGE}px;
            color: white;
            font-weight: bold;
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}
        
        QPushButton:hover {{
            background-color: {Colors.ACCENT_HOVER};
        }}
        
        QPushButton:pressed {{
            background-color: {Colors.ACCENT_PRESSED};
        }}
        
        QPushButton:disabled {{
            background-color: {Colors.SECONDARY_TEXT};
            color: {Colors.BORDER};
        }}
        """
    
    @staticmethod
    def get_input_style():
        """输入框样式"""
        return f"""
        QLineEdit, QComboBox, QSpinBox {{
            background-color: {Colors.INPUT_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            padding: {Styles.PADDING_NORMAL}px;
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}

        QLineEdit:focus, QComboBox:focus, QSpinBox:focus {{
            border-color: {Colors.ACCENT};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 20px;
            background-color: {Colors.INPUT_BG};
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {Colors.FOREGROUND};
        }}

        QComboBox QAbstractItemView {{
            background-color: {Colors.INPUT_BG};
            border: 1px solid {Colors.BORDER};
            border-radius: {Styles.INPUT_RADIUS};
            color: {Colors.FOREGROUND};
            selection-background-color: {Colors.ACCENT};
            selection-color: white;
            padding: {Styles.PADDING_SMALL}px;
        }}

        QComboBox QAbstractItemView::item {{
            padding: {Styles.PADDING_SMALL}px;
            min-height: 20px;
        }}

        QComboBox QAbstractItemView::item:hover {{
            background-color: {Colors.SECONDARY_BG};
        }}

        QComboBox QAbstractItemView::item:selected {{
            background-color: {Colors.ACCENT};
            color: white;
        }}

        QSpinBox::up-button, QSpinBox::down-button {{
            background-color: {Colors.SECONDARY_BG};
            border: 1px solid {Colors.BORDER};
            width: 16px;
        }}

        QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
            background-color: {Colors.ACCENT};
        }}

        QSpinBox::up-arrow, QSpinBox::down-arrow {{
            width: 8px;
            height: 8px;
        }}

        QSpinBox::up-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 4px solid {Colors.FOREGROUND};
        }}

        QSpinBox::down-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid {Colors.FOREGROUND};
        }}
        """
    
    @staticmethod
    def get_slider_style():
        """滑块样式"""
        return f"""
        QSlider::groove:horizontal {{
            background-color: {Colors.SECONDARY_BG};
            height: 6px;
            border-radius: 3px;
        }}
        
        QSlider::handle:horizontal {{
            background-color: {Colors.ACCENT};
            border: 2px solid {Colors.ACCENT};
            width: 16px;
            height: 16px;
            border-radius: 8px;
            margin: -5px 0;
        }}
        
        QSlider::handle:horizontal:hover {{
            background-color: {Colors.ACCENT_HOVER};
            border-color: {Colors.ACCENT_HOVER};
        }}
        
        QSlider::sub-page:horizontal {{
            background-color: {Colors.ACCENT};
            border-radius: 3px;
        }}
        """
    
    @staticmethod
    def get_label_style():
        """标签样式"""
        return f"""
        QLabel {{
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_NORMAL}px;
        }}

        QLabel[class="title"] {{
            font-size: {Styles.FONT_SIZE_TITLE}px;
            font-weight: bold;
            color: {Colors.FOREGROUND};
        }}

        QLabel[class="header"] {{
            font-size: {Styles.FONT_SIZE_HEADER}px;
            font-weight: bold;
            color: {Colors.FOREGROUND};
        }}

        QLabel[class="secondary"] {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_SMALL}px;
        }}

        QLabel[class="label-text"] {{
            color: {Colors.SECONDARY_TEXT};
            font-size: {Styles.FONT_SIZE_SMALL}px;
            margin-bottom: {Styles.MARGIN_SMALL}px;
        }}
        """
    
    @staticmethod
    def get_checkbox_style():
        """复选框样式"""
        return f"""
        QCheckBox {{
            color: {Colors.FOREGROUND};
            font-size: {Styles.FONT_SIZE_NORMAL}px;
            spacing: {Styles.MARGIN_SMALL}px;
        }}

        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border-radius: 4px;
            border: 2px solid {Colors.BORDER};
            background-color: {Colors.INPUT_BG};
        }}

        QCheckBox::indicator:hover {{
            border-color: {Colors.ACCENT};
        }}

        QCheckBox::indicator:checked {{
            background-color: {Colors.ACCENT};
            border-color: {Colors.ACCENT};
        }}

        QCheckBox::indicator:checked:hover {{
            background-color: {Colors.ACCENT_HOVER};
            border-color: {Colors.ACCENT_HOVER};
        }}
        """

    @staticmethod
    def get_upload_widget_style():
        """上传组件样式"""
        return f"""
        QFrame[class="upload-widget"] {{
            background-color: {Colors.CARD_BG};
            border: 2px dashed {Colors.ACCENT};
            border-radius: {Styles.CARD_RADIUS};
            padding: {Styles.PADDING_LARGE * 2}px;
        }}

        QFrame[class="upload-widget"]:hover {{
            border-color: {Colors.ACCENT_HOVER};
            background-color: {Colors.SECONDARY_BG};
        }}
        """
    
    @staticmethod
    def get_complete_stylesheet():
        """获取完整样式表"""
        return "\n".join([
            StyleManager.get_main_window_style(),
            StyleManager.get_splitter_style(),
            StyleManager.get_panel_style(),
            StyleManager.get_accordion_style(),
            StyleManager.get_button_style(),
            StyleManager.get_input_style(),
            StyleManager.get_slider_style(),
            StyleManager.get_label_style(),
            StyleManager.get_checkbox_style(),
            StyleManager.get_upload_widget_style(),
        ])
